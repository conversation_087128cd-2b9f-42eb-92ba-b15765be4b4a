"""
Decision Engine for Clonely AI
Analyzes conversations and decides what actions to take
"""

import asyncio
import re
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import structlog

from core.memory_manager import MemoryManager

logger = structlog.get_logger()


class DecisionEngine:
    """
    Analyzes user messages and AI responses to determine what actions should be taken
    This is where the AI becomes proactive and operational
    """
    
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self._initialized = False
        
        # Action patterns and triggers
        self.action_patterns = {
            "schedule_meeting": [
                r"schedule.*meeting",
                r"set up.*call",
                r"book.*time",
                r"arrange.*meeting",
                r"let's meet"
            ],
            "send_email": [
                r"send.*email",
                r"email.*about",
                r"reach out.*to",
                r"contact.*via email",
                r"follow up.*email"
            ],
            "create_task": [
                r"remind me",
                r"add.*to.*list",
                r"create.*task",
                r"don't forget",
                r"make sure.*to"
            ],
            "update_notion": [
                r"update.*notion",
                r"add.*to notion",
                r"document.*in",
                r"note.*in notion",
                r"track.*in notion"
            ],
            "slack_message": [
                r"message.*team",
                r"slack.*about",
                r"tell.*team",
                r"update.*team",
                r"notify.*team"
            ],
            "calendar_block": [
                r"block.*time",
                r"reserve.*calendar",
                r"schedule.*focus time",
                r"book.*calendar",
                r"protect.*time"
            ]
        }
        
        # Priority keywords
        self.priority_keywords = {
            "urgent": ["urgent", "asap", "immediately", "critical", "emergency"],
            "high": ["important", "priority", "soon", "today", "this week"],
            "medium": ["when possible", "next week", "sometime", "eventually"],
            "low": ["someday", "maybe", "if time", "low priority"]
        }
    
    async def initialize(self):
        """Initialize the decision engine"""
        self._initialized = True
        logger.info("Decision engine initialized")
    
    async def analyze_for_actions(
        self,
        user_id: str,
        user_message: str,
        ai_response: str
    ) -> List[Dict[str, Any]]:
        """
        Analyze a conversation exchange and determine what actions should be taken
        """
        try:
            actions = []
            
            # Analyze user message for explicit action requests
            user_actions = await self._extract_actions_from_text(user_message, "user")
            actions.extend(user_actions)
            
            # Analyze AI response for suggested actions
            ai_actions = await self._extract_actions_from_text(ai_response, "ai")
            actions.extend(ai_actions)
            
            # Apply user preferences and patterns
            actions = await self._apply_user_preferences(user_id, actions)
            
            # Prioritize and filter actions
            actions = await self._prioritize_actions(actions)
            
            logger.info(
                "Actions analyzed",
                user_id=user_id,
                actions_found=len(actions)
            )
            
            return actions
            
        except Exception as e:
            logger.error("Failed to analyze for actions", user_id=user_id, error=str(e))
            return []
    
    async def _extract_actions_from_text(
        self,
        text: str,
        source: str
    ) -> List[Dict[str, Any]]:
        """Extract potential actions from text using pattern matching"""
        actions = []
        text_lower = text.lower()
        
        for action_type, patterns in self.action_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text_lower)
                for match in matches:
                    # Extract context around the match
                    start = max(0, match.start() - 50)
                    end = min(len(text), match.end() + 100)
                    context = text[start:end].strip()
                    
                    # Determine priority
                    priority = self._determine_priority(context)
                    
                    # Extract specific details based on action type
                    details = await self._extract_action_details(action_type, context, text)
                    
                    action = {
                        "type": action_type,
                        "context": context,
                        "priority": priority,
                        "source": source,
                        "confidence": self._calculate_confidence(pattern, context),
                        "details": details,
                        "extracted_at": datetime.utcnow().isoformat()
                    }
                    
                    actions.append(action)
        
        return actions
    
    def _determine_priority(self, text: str) -> str:
        """Determine priority based on keywords in text"""
        text_lower = text.lower()
        
        for priority, keywords in self.priority_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return priority
        
        return "medium"  # Default priority
    
    def _calculate_confidence(self, pattern: str, context: str) -> float:
        """Calculate confidence score for action extraction"""
        base_confidence = 0.7
        
        # Increase confidence for specific patterns
        if any(word in context.lower() for word in ["please", "need to", "must", "should"]):
            base_confidence += 0.1
        
        # Decrease confidence for uncertain language
        if any(word in context.lower() for word in ["maybe", "might", "could", "perhaps"]):
            base_confidence -= 0.2
        
        return max(0.1, min(1.0, base_confidence))
    
    async def _extract_action_details(
        self,
        action_type: str,
        context: str,
        full_text: str
    ) -> Dict[str, Any]:
        """Extract specific details for different action types"""
        details = {}
        
        if action_type == "schedule_meeting":
            details.update({
                "participants": self._extract_participants(full_text),
                "duration": self._extract_duration(context),
                "topic": self._extract_topic(context),
                "suggested_times": self._extract_times(context)
            })
        
        elif action_type == "send_email":
            details.update({
                "recipients": self._extract_email_recipients(full_text),
                "subject": self._extract_email_subject(context),
                "key_points": self._extract_key_points(context)
            })
        
        elif action_type == "create_task":
            details.update({
                "task_description": self._extract_task_description(context),
                "due_date": self._extract_due_date(context),
                "assignee": self._extract_assignee(full_text)
            })
        
        elif action_type == "update_notion":
            details.update({
                "page_type": self._extract_notion_page_type(context),
                "content": self._extract_notion_content(context),
                "database": self._extract_notion_database(context)
            })
        
        elif action_type == "slack_message":
            details.update({
                "channel": self._extract_slack_channel(full_text),
                "message_content": self._extract_slack_content(context),
                "mentions": self._extract_mentions(full_text)
            })
        
        elif action_type == "calendar_block":
            details.update({
                "duration": self._extract_duration(context),
                "purpose": self._extract_block_purpose(context),
                "preferred_times": self._extract_times(context)
            })
        
        return details
    
    def _extract_participants(self, text: str) -> List[str]:
        """Extract meeting participants from text"""
        # Simple extraction - in production, this would be more sophisticated
        participants = []
        
        # Look for common patterns
        patterns = [
            r"with\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)",
            r"invite\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)",
            r"include\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)"
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            participants.extend(matches)
        
        return list(set(participants))  # Remove duplicates
    
    def _extract_duration(self, text: str) -> Optional[str]:
        """Extract duration from text"""
        duration_patterns = [
            r"(\d+)\s*(?:hour|hr)s?",
            r"(\d+)\s*(?:minute|min)s?",
            r"(\d+)\s*(?:day)s?",
            r"(half|30)\s*(?:hour|hr)",
            r"(quarter|15)\s*(?:minute|min)"
        ]
        
        for pattern in duration_patterns:
            match = re.search(pattern, text.lower())
            if match:
                return match.group(0)
        
        return None
    
    def _extract_topic(self, text: str) -> Optional[str]:
        """Extract meeting topic from context"""
        # Look for phrases that indicate topic
        topic_patterns = [
            r"about\s+(.+?)(?:\.|,|$)",
            r"regarding\s+(.+?)(?:\.|,|$)",
            r"to discuss\s+(.+?)(?:\.|,|$)"
        ]
        
        for pattern in topic_patterns:
            match = re.search(pattern, text.lower())
            if match:
                return match.group(1).strip()
        
        return None
    
    def _extract_times(self, text: str) -> List[str]:
        """Extract time references from text"""
        time_patterns = [
            r"\d{1,2}:\d{2}\s*(?:am|pm)",
            r"\d{1,2}\s*(?:am|pm)",
            r"(?:monday|tuesday|wednesday|thursday|friday|saturday|sunday)",
            r"(?:tomorrow|today|next week|this week)",
            r"(?:morning|afternoon|evening)"
        ]
        
        times = []
        for pattern in time_patterns:
            matches = re.findall(pattern, text.lower())
            times.extend(matches)
        
        return times
    
    def _extract_email_recipients(self, text: str) -> List[str]:
        """Extract email recipients"""
        # Look for email addresses and names
        email_pattern = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
        emails = re.findall(email_pattern, text)
        
        # Also look for names after "email" or "contact"
        name_patterns = [
            r"email\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)",
            r"contact\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)"
        ]
        
        names = []
        for pattern in name_patterns:
            matches = re.findall(pattern, text)
            names.extend(matches)
        
        return emails + names
    
    def _extract_email_subject(self, text: str) -> Optional[str]:
        """Extract email subject from context"""
        subject_patterns = [
            r"subject:\s*(.+?)(?:\.|,|$)",
            r"about\s+(.+?)(?:\.|,|$)",
            r"regarding\s+(.+?)(?:\.|,|$)"
        ]
        
        for pattern in subject_patterns:
            match = re.search(pattern, text.lower())
            if match:
                return match.group(1).strip()
        
        return None
    
    def _extract_key_points(self, text: str) -> List[str]:
        """Extract key points for email content"""
        # Simple extraction of sentences that might be key points
        sentences = text.split('.')
        key_points = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10 and any(word in sentence.lower() for word in [
                "important", "need", "should", "must", "update", "inform"
            ]):
                key_points.append(sentence)
        
        return key_points[:3]  # Limit to 3 key points
    
    def _extract_task_description(self, text: str) -> str:
        """Extract task description from context"""
        # Remove action words and return the core description
        cleaned = re.sub(r"^(remind me to|add to list|create task|don't forget to|make sure to)\s*", "", text.lower())
        return cleaned.strip()
    
    def _extract_due_date(self, text: str) -> Optional[str]:
        """Extract due date from text"""
        date_patterns = [
            r"by\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday)",
            r"by\s+(tomorrow|today|next week|this week)",
            r"due\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday)",
            r"before\s+(.+?)(?:\.|,|$)"
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text.lower())
            if match:
                return match.group(1)
        
        return None
    
    def _extract_assignee(self, text: str) -> Optional[str]:
        """Extract task assignee"""
        assignee_patterns = [
            r"assign to\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)",
            r"give to\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)"
        ]
        
        for pattern in assignee_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_notion_page_type(self, text: str) -> Optional[str]:
        """Extract Notion page type"""
        if any(word in text.lower() for word in ["database", "table"]):
            return "database"
        elif any(word in text.lower() for word in ["page", "document"]):
            return "page"
        return None
    
    def _extract_notion_content(self, text: str) -> str:
        """Extract content for Notion"""
        return text.strip()
    
    def _extract_notion_database(self, text: str) -> Optional[str]:
        """Extract Notion database name"""
        db_patterns = [
            r"in\s+(.+?)\s+database",
            r"to\s+(.+?)\s+table",
            r"(.+?)\s+notion"
        ]
        
        for pattern in db_patterns:
            match = re.search(pattern, text.lower())
            if match:
                return match.group(1).strip()
        
        return None
    
    def _extract_slack_channel(self, text: str) -> Optional[str]:
        """Extract Slack channel"""
        channel_patterns = [
            r"#([a-z0-9-_]+)",
            r"in\s+([a-z0-9-_]+)\s+channel",
            r"to\s+([a-z0-9-_]+)\s+team"
        ]
        
        for pattern in channel_patterns:
            match = re.search(pattern, text.lower())
            if match:
                return match.group(1)
        
        return None
    
    def _extract_slack_content(self, text: str) -> str:
        """Extract Slack message content"""
        return text.strip()
    
    def _extract_mentions(self, text: str) -> List[str]:
        """Extract @mentions from text"""
        mention_pattern = r"@([a-zA-Z0-9_]+)"
        return re.findall(mention_pattern, text)
    
    def _extract_block_purpose(self, text: str) -> Optional[str]:
        """Extract purpose for calendar block"""
        purpose_patterns = [
            r"for\s+(.+?)(?:\.|,|$)",
            r"to\s+(.+?)(?:\.|,|$)",
            r"focus\s+on\s+(.+?)(?:\.|,|$)"
        ]
        
        for pattern in purpose_patterns:
            match = re.search(pattern, text.lower())
            if match:
                return match.group(1).strip()
        
        return None
    
    async def _apply_user_preferences(
        self,
        user_id: str,
        actions: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Apply user preferences to filter and modify actions"""
        try:
            # Get user preferences from memory
            preferences = await self.memory_manager.search_memories(
                user_id=user_id,
                query="preferences automation actions",
                memory_types=["preference"],
                limit=10
            )
            
            # Apply preferences (simplified logic)
            filtered_actions = []
            for action in actions:
                # Check if user has disabled this type of action
                action_disabled = any(
                    f"disable {action['type']}" in pref["content"].lower()
                    for pref in preferences
                )
                
                if not action_disabled:
                    filtered_actions.append(action)
            
            return filtered_actions
            
        except Exception as e:
            logger.error("Failed to apply user preferences", user_id=user_id, error=str(e))
            return actions
    
    async def _prioritize_actions(self, actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prioritize and filter actions based on confidence and priority"""
        # Filter out low-confidence actions
        high_confidence_actions = [
            action for action in actions
            if action.get("confidence", 0) >= 0.6
        ]
        
        # Sort by priority and confidence
        priority_order = {"urgent": 4, "high": 3, "medium": 2, "low": 1}
        
        sorted_actions = sorted(
            high_confidence_actions,
            key=lambda x: (
                priority_order.get(x.get("priority", "medium"), 2),
                x.get("confidence", 0)
            ),
            reverse=True
        )
        
        # Limit to top 5 actions to avoid overwhelming the user
        return sorted_actions[:5]
