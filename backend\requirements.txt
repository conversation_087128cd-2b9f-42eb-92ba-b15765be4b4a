# Core FastAPI and async support
fastapi==0.104.1
uvicorn[standard]==0.24.0
websockets==12.0
python-multipart==0.0.6
pydantic-settings==2.1.0

# AI/ML and LLM integration
openai==1.3.7
anthropic==0.7.8
langchain==0.0.350
langchain-openai==0.0.2
langchain-anthropic==0.0.1
sentence-transformers==2.2.2
numpy==1.24.3
torch==2.1.1

# Database and vector storage
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23
alembic==1.12.1
pgvector==0.2.4
redis==5.0.1

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0

# HTTP client and integrations
httpx==0.25.2
aiofiles==23.2.1

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
mypy==1.7.1

# Monitoring and logging
structlog==23.2.0
