"""
Clonely Backend - AI-Powered Operating System for Startup Founders
Main FastAPI application with WebSocket support and AI agent integration
"""

from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import asyncio
import json
import logging
from typing import Dict, List
import structlog

from core.ai_agent import ClonelyAgent
from core.memory_manager import MemoryManager
from core.websocket_manager import ConnectionManager
from api.routes import auth, agent, integrations
from database.connection import get_db_session
from config.settings import get_settings

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Initialize FastAPI app
app = FastAPI(
    title="Clonely API",
    description="AI-Powered Operating System for Startup Founders",
    version="0.1.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize core components
settings = get_settings()
connection_manager = ConnectionManager()
memory_manager = MemoryManager()
clonely_agent = ClonelyAgent(memory_manager=memory_manager)

# Include API routes
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(agent.router, prefix="/api/agent", tags=["ai-agent"])
app.include_router(integrations.router, prefix="/api/integrations", tags=["integrations"])


@app.on_event("startup")
async def startup_event():
    """Initialize application components on startup"""
    logger.info("Starting Clonely backend...")
    
    # Initialize database connections
    await memory_manager.initialize()
    
    # Initialize AI agent
    await clonely_agent.initialize()
    
    logger.info("Clonely backend started successfully")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on application shutdown"""
    logger.info("Shutting down Clonely backend...")
    
    # Close database connections
    await memory_manager.close()
    
    logger.info("Clonely backend shutdown complete")


@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Clonely AI Operating System", "status": "running", "version": "0.1.0"}


@app.get("/api/health")
async def health_check():
    """Detailed health check for monitoring"""
    try:
        # Check database connectivity
        db_status = await memory_manager.health_check()
        
        # Check AI agent status
        agent_status = await clonely_agent.health_check()
        
        return {
            "status": "healthy",
            "components": {
                "database": db_status,
                "ai_agent": agent_status,
                "websockets": "active"
            }
        }
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return JSONResponse(
            status_code=503,
            content={"status": "unhealthy", "error": str(e)}
        )


@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """
    WebSocket endpoint for real-time communication with the AI agent
    Enables the 'operating system' experience with instant responses
    """
    await connection_manager.connect(websocket, user_id)
    logger.info("WebSocket connected", user_id=user_id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            logger.info("Received message", user_id=user_id, message_type=message_data.get("type"))
            
            # Process message through AI agent
            response = await clonely_agent.process_message(
                user_id=user_id,
                message=message_data,
                websocket=websocket
            )
            
            # Send response back to client
            await connection_manager.send_personal_message(
                message=json.dumps(response),
                user_id=user_id
            )
            
    except WebSocketDisconnect:
        connection_manager.disconnect(user_id)
        logger.info("WebSocket disconnected", user_id=user_id)
    except Exception as e:
        logger.error("WebSocket error", user_id=user_id, error=str(e))
        connection_manager.disconnect(user_id)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_config=None  # Use structlog instead
    )
