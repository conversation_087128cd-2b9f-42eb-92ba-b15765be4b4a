version: '3.8'

services:
  # PostgreSQL database with pgvector extension
  postgres:
    image: pgvector/pgvector:pg15
    container_name: clonely-postgres
    environment:
      POSTGRES_DB: clonely
      POSTGRES_USER: clonely
      POSTGRES_PASSWORD: clonely
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U clonely -d clonely"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - clonely-network

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: clonely-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - clonely-network

  # FastAPI backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: clonely-backend
    environment:
      - DATABASE_URL=postgresql+asyncpg://clonely:clonely@postgres:5432/clonely
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=true
      - ENVIRONMENT=development
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/__pycache__
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - clonely-network

  # Next.js frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: clonely-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_WS_URL=ws://localhost:8000
      - NODE_ENV=development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    command: npm run dev
    networks:
      - clonely-network

  # Nginx reverse proxy (optional, for production-like setup)
  nginx:
    image: nginx:alpine
    container_name: clonely-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - clonely-network
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  clonely-network:
    driver: bridge
