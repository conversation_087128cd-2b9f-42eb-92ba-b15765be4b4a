'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Brain, MessageSquare, Zap, Users, ArrowRight, Mi<PERSON>, MicOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ChatInterface } from '@/components/chat/chat-interface';
import { useWebSocket } from '@/hooks/use-websocket';
import { useAuth } from '@/hooks/use-auth';

export default function HomePage() {
  const [showChat, setShowChat] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const { user, login } = useAuth();
  const { isConnected, sendMessage, messages } = useWebSocket(user?.id);

  const features = [
    {
      icon: Brain,
      title: 'AI Second Brain',
      description: 'Learns your thinking patterns, preferences, and decision-making style to act as your cognitive extension.',
    },
    {
      icon: MessageSquare,
      title: 'Executive Assistant',
      description: '<PERSON>les communications, schedules meetings, writes updates, and manages your daily workflow seamlessly.',
    },
    {
      icon: Zap,
      title: 'Strategic Operator',
      description: 'Executes tasks across platforms like Notion, Slack, and email while maintaining your strategic vision.',
    },
    {
      icon: Users,
      title: 'Team Coordinator',
      description: 'Manages team communications and ensures everyone stays aligned with your goals and priorities.',
    },
  ];

  const handleStartConversation = async () => {
    if (!user) {
      // For demo purposes, create a temporary user
      await login('demo-founder', 'Demo Founder');
    }
    setShowChat(true);
  };

  const toggleVoiceInput = () => {
    setIsListening(!isListening);
    // Voice recognition logic will be implemented here
  };

  if (showChat) {
    return (
      <div className="h-screen bg-gray-900">
        <ChatInterface
          isConnected={isConnected}
          messages={messages}
          onSendMessage={sendMessage}
          onClose={() => setShowChat(false)}
          isListening={isListening}
          onToggleVoice={toggleVoiceInput}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
              Meet Your
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                {' '}AI Clone
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
              An AI-powered operating system that thinks, acts, and executes like you. 
              Your second brain, executive assistant, strategist, and operator — all in one.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                onClick={handleStartConversation}
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Start Conversation
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              
              <Button
                variant="outline"
                size="lg"
                onClick={toggleVoiceInput}
                className="border-white/20 text-white hover:bg-white/10 px-8 py-4 text-lg font-semibold rounded-xl"
              >
                {isListening ? (
                  <>
                    <MicOff className="mr-2 h-5 w-5" />
                    Stop Listening
                  </>
                ) : (
                  <>
                    <Mic className="mr-2 h-5 w-5" />
                    Voice Mode
                  </>
                )}
              </Button>
            </div>

            {isConnected && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mt-4 text-green-400 font-medium"
              >
                ✓ Connected to Clonely AI
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-white mb-4">
            10x Your Productivity
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Clonely learns your patterns, automates your workflows, and scales your decision-making 
            across every aspect of your startup.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 * index }}
            >
              <Card className="bg-white/10 backdrop-blur-sm border-white/20 p-6 h-full hover:bg-white/15 transition-all duration-200">
                <feature.icon className="h-12 w-12 text-blue-400 mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-300 leading-relaxed">
                  {feature.description}
                </p>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Clone Yourself?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join the future of startup operations. Let Clonely handle the execution 
            while you focus on the vision.
          </p>
          <Button
            onClick={handleStartConversation}
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
          >
            Get Started Now
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </motion.div>
      </div>
    </div>
  );
}
