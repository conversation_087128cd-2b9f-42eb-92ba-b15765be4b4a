"""
Database models for Clonely AI Operating System
Defines the core data structures for users, memories, conversations, and integrations
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, JSON, ForeignKey, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from pgvector.sqlalchemy import Vector
import uuid
from datetime import datetime

Base = declarative_base()


class User(Base):
    """User model for startup founders using Clonely"""
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(255), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # Profile and preferences
    company_name = Column(String(255))
    role = Column(String(100))
    timezone = Column(String(50), default="UTC")
    preferences = Column(JSON, default={})
    
    # AI learning and personalization
    communication_style = Column(JSON, default={})
    decision_patterns = Column(JSON, default={})
    workflow_preferences = Column(JSON, default={})
    
    # Account status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_active = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    conversations = relationship("Conversation", back_populates="user", cascade="all, delete-orphan")
    memories = relationship("Memory", back_populates="user", cascade="all, delete-orphan")
    integrations = relationship("Integration", back_populates="user", cascade="all, delete-orphan")


class Conversation(Base):
    """Conversation sessions between user and AI agent"""
    __tablename__ = "conversations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    title = Column(String(255))
    summary = Column(Text)
    context = Column(JSON, default={})
    
    # Conversation metadata
    message_count = Column(Integer, default=0)
    total_tokens = Column(Integer, default=0)
    
    # Status and timing
    status = Column(String(50), default="active")  # active, archived, deleted
    started_at = Column(DateTime, default=datetime.utcnow)
    last_message_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")


class Message(Base):
    """Individual messages within conversations"""
    __tablename__ = "messages"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id"), nullable=False)
    
    # Message content
    content = Column(Text, nullable=False)
    message_type = Column(String(50), nullable=False)  # user, assistant, system, tool
    role = Column(String(50), nullable=False)  # user, assistant, system
    
    # Message metadata
    tokens = Column(Integer, default=0)
    model_used = Column(String(100))
    processing_time = Column(Float)
    
    # Voice and multimedia
    has_audio = Column(Boolean, default=False)
    audio_duration = Column(Float)
    attachments = Column(JSON, default=[])
    
    # AI processing metadata
    intent = Column(String(100))
    entities = Column(JSON, default=[])
    sentiment = Column(String(50))
    confidence = Column(Float)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    conversation = relationship("Conversation", back_populates="messages")


class Memory(Base):
    """AI memory system for learning user patterns and preferences"""
    __tablename__ = "memories"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Memory content
    content = Column(Text, nullable=False)
    memory_type = Column(String(100), nullable=False)  # preference, pattern, fact, decision, workflow
    category = Column(String(100))  # communication, decision_making, workflow, personal, business
    
    # Vector embedding for semantic search
    embedding = Column(Vector(384))  # 384 dimensions for sentence-transformers/all-MiniLM-L6-v2
    
    # Memory metadata
    importance_score = Column(Float, default=0.5)  # 0.0 to 1.0
    confidence = Column(Float, default=0.5)  # How confident we are in this memory
    access_count = Column(Integer, default=0)
    
    # Source and context
    source_type = Column(String(100))  # conversation, integration, manual, inferred
    source_id = Column(UUID(as_uuid=True))  # ID of source (conversation, message, etc.)
    context = Column(JSON, default={})
    
    # Temporal information
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_accessed = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)  # Optional expiration for temporary memories
    
    # Relationships
    user = relationship("User", back_populates="memories")


class Integration(Base):
    """Platform integrations (Notion, Slack, Email, etc.)"""
    __tablename__ = "integrations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Integration details
    platform = Column(String(100), nullable=False)  # notion, slack, gmail, calendar, etc.
    platform_user_id = Column(String(255))
    
    # Authentication and configuration
    access_token = Column(Text)  # Encrypted
    refresh_token = Column(Text)  # Encrypted
    token_expires_at = Column(DateTime)
    configuration = Column(JSON, default={})
    
    # Integration status
    is_active = Column(Boolean, default=True)
    last_sync = Column(DateTime)
    sync_status = Column(String(50), default="pending")  # pending, syncing, success, error
    error_message = Column(Text)
    
    # Permissions and scope
    scopes = Column(ARRAY(String))
    permissions = Column(JSON, default={})
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="integrations")


class Task(Base):
    """AI-managed tasks and actions"""
    __tablename__ = "tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Task details
    title = Column(String(255), nullable=False)
    description = Column(Text)
    task_type = Column(String(100), nullable=False)  # email, calendar, notion, slack, etc.
    
    # Task execution
    status = Column(String(50), default="pending")  # pending, in_progress, completed, failed, cancelled
    priority = Column(String(50), default="medium")  # low, medium, high, urgent
    
    # Scheduling
    scheduled_for = Column(DateTime)
    due_date = Column(DateTime)
    completed_at = Column(DateTime)
    
    # Execution details
    platform = Column(String(100))  # Which integration to use
    action_data = Column(JSON, default={})  # Platform-specific action data
    result = Column(JSON, default={})  # Execution result
    error_message = Column(Text)
    
    # AI decision context
    reasoning = Column(Text)  # Why the AI decided to create this task
    confidence = Column(Float, default=0.5)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class Analytics(Base):
    """Analytics and insights about user behavior and AI performance"""
    __tablename__ = "analytics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Event details
    event_type = Column(String(100), nullable=False)  # conversation, task, integration, etc.
    event_data = Column(JSON, default={})
    
    # Metrics
    duration = Column(Float)  # Duration in seconds
    tokens_used = Column(Integer)
    cost = Column(Float)  # Cost in USD
    
    # Performance metrics
    response_time = Column(Float)
    accuracy_score = Column(Float)
    user_satisfaction = Column(Float)  # 0.0 to 1.0
    
    created_at = Column(DateTime, default=datetime.utcnow)
    date = Column(DateTime, default=datetime.utcnow)  # For date-based aggregations
