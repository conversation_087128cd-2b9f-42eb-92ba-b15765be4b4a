{"name": "clonely-frontend", "version": "0.1.0", "description": "Clonely AI Operating System - Frontend Interface", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"next": "14.0.3", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "5.3.2", "@types/node": "20.9.0", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "tailwindcss": "3.3.5", "autoprefixer": "10.4.16", "postcss": "8.4.31", "@tailwindcss/forms": "0.5.7", "@tailwindcss/typography": "0.5.10", "framer-motion": "10.16.5", "lucide-react": "0.294.0", "@radix-ui/react-dialog": "1.0.5", "@radix-ui/react-dropdown-menu": "2.0.6", "@radix-ui/react-toast": "1.1.5", "@radix-ui/react-tooltip": "1.0.7", "@radix-ui/react-avatar": "1.0.4", "socket.io-client": "4.7.4", "axios": "1.6.2", "swr": "2.2.4", "zustand": "4.4.7", "date-fns": "2.30.0", "clsx": "2.0.0", "class-variance-authority": "0.7.0", "react-speech-recognition": "3.10.0", "react-use-websocket": "4.5.0", "@next/font": "14.0.3"}, "devDependencies": {"eslint": "8.54.0", "eslint-config-next": "14.0.3", "@typescript-eslint/eslint-plugin": "6.12.0", "@typescript-eslint/parser": "6.12.0", "prettier": "3.1.0", "prettier-plugin-tailwindcss": "0.5.7", "jest": "29.7.0", "@testing-library/react": "14.1.2", "@testing-library/jest-dom": "6.1.5", "jest-environment-jsdom": "29.7.0", "@types/jest": "29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}