"""
Core AI Agent for Clonely
The central intelligence that acts as the founder's clone
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
import structlog

from langchain.schema import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain.chat_models import ChatOpenAI, ChatAnthropic
from langchain.memory import ConversationBufferWindowMemory
from langchain.callbacks.base import BaseCallbackHandler

from core.memory_manager import MemoryManager
from core.decision_engine import DecisionEngine
from core.task_executor import TaskExecutor
from config.settings import get_settings

logger = structlog.get_logger()


class ClonelyCallbackHandler(BaseCallbackHandler):
    """Custom callback handler for tracking AI agent performance"""
    
    def __init__(self, user_id: str, websocket=None):
        self.user_id = user_id
        self.websocket = websocket
        self.start_time = None
        self.tokens_used = 0
    
    async def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs):
        """Called when LLM starts processing"""
        self.start_time = datetime.utcnow()
        if self.websocket:
            await self.websocket.send_text(json.dumps({
                "type": "agent_status",
                "status": "thinking",
                "message": "Processing your request..."
            }))
    
    async def on_llm_end(self, response, **kwargs):
        """Called when LLM finishes processing"""
        if self.websocket:
            await self.websocket.send_text(json.dumps({
                "type": "agent_status",
                "status": "ready",
                "message": "Response ready"
            }))


class ClonelyAgent:
    """
    The core AI agent that acts as the founder's clone
    Integrates memory, decision-making, and task execution
    """
    
    def __init__(self, memory_manager: MemoryManager):
        self.settings = get_settings()
        self.memory_manager = memory_manager
        self.decision_engine = DecisionEngine(memory_manager)
        self.task_executor = TaskExecutor()
        
        # LLM setup
        self.llm = None
        self.conversation_memory = {}  # Per-user conversation memory
        
        # Agent personality and behavior
        self.system_prompt = self._build_system_prompt()
        
        self._initialized = False
    
    async def initialize(self):
        """Initialize the AI agent"""
        if self._initialized:
            return
        
        try:
            # Initialize LLM based on settings
            if self.settings.default_llm_provider == "openai":
                self.llm = ChatOpenAI(
                    model_name=self.settings.default_model,
                    openai_api_key=self.settings.openai_api_key,
                    temperature=0.7,
                    max_tokens=2000
                )
            elif self.settings.default_llm_provider == "anthropic":
                self.llm = ChatAnthropic(
                    model=self.settings.default_model,
                    anthropic_api_key=self.settings.anthropic_api_key,
                    temperature=0.7,
                    max_tokens=2000
                )
            else:
                raise ValueError(f"Unsupported LLM provider: {self.settings.default_llm_provider}")
            
            # Initialize sub-components
            await self.decision_engine.initialize()
            await self.task_executor.initialize()
            
            self._initialized = True
            logger.info("Clonely AI agent initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize AI agent", error=str(e))
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Check AI agent health"""
        try:
            if not self._initialized:
                return {"status": "not_initialized"}
            
            # Test LLM connectivity
            test_response = await self.llm.agenerate([[HumanMessage(content="Hello")]])
            
            return {
                "status": "healthy",
                "llm_provider": self.settings.default_llm_provider,
                "model": self.settings.default_model,
                "initialized": self._initialized,
                "test_response_length": len(test_response.generations[0][0].text)
            }
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}
    
    async def process_message(
        self,
        user_id: str,
        message: Dict[str, Any],
        websocket=None
    ) -> Dict[str, Any]:
        """
        Process a message from the user and generate a response
        This is the main entry point for user interactions
        """
        try:
            message_content = message.get("content", "")
            message_type = message.get("type", "text")
            
            logger.info(
                "Processing message",
                user_id=user_id,
                message_type=message_type,
                content_length=len(message_content)
            )
            
            # Initialize user conversation memory if needed
            if user_id not in self.conversation_memory:
                self.conversation_memory[user_id] = ConversationBufferWindowMemory(
                    k=10,  # Keep last 10 exchanges
                    return_messages=True
                )
            
            # Get user's memory context
            relevant_memories = await self.memory_manager.search_memories(
                user_id=user_id,
                query=message_content,
                limit=5
            )
            
            # Build context-aware prompt
            context_prompt = await self._build_context_prompt(
                user_id=user_id,
                message_content=message_content,
                relevant_memories=relevant_memories
            )
            
            # Generate response using LLM
            callback_handler = ClonelyCallbackHandler(user_id, websocket)
            
            messages = [
                SystemMessage(content=self.system_prompt),
                SystemMessage(content=context_prompt),
                HumanMessage(content=message_content)
            ]
            
            # Add conversation history
            memory_messages = self.conversation_memory[user_id].chat_memory.messages
            if memory_messages:
                messages.extend(memory_messages[-6:])  # Last 3 exchanges
            
            # Generate response
            response = await self.llm.agenerate(
                [messages],
                callbacks=[callback_handler]
            )
            
            ai_response = response.generations[0][0].text
            
            # Update conversation memory
            self.conversation_memory[user_id].chat_memory.add_user_message(message_content)
            self.conversation_memory[user_id].chat_memory.add_ai_message(ai_response)
            
            # Analyze message for learning opportunities
            await self._analyze_for_learning(user_id, message_content, ai_response)
            
            # Check if any actions need to be taken
            actions = await self.decision_engine.analyze_for_actions(
                user_id=user_id,
                user_message=message_content,
                ai_response=ai_response
            )
            
            # Execute any identified actions
            action_results = []
            for action in actions:
                result = await self.task_executor.execute_action(user_id, action)
                action_results.append(result)
            
            # Build response
            response_data = {
                "type": "assistant_response",
                "content": ai_response,
                "timestamp": datetime.utcnow().isoformat(),
                "actions_taken": len(action_results),
                "actions": action_results,
                "memories_used": len(relevant_memories),
                "model_used": self.settings.default_model
            }
            
            logger.info(
                "Message processed successfully",
                user_id=user_id,
                response_length=len(ai_response),
                actions_taken=len(action_results)
            )
            
            return response_data
            
        except Exception as e:
            logger.error("Failed to process message", user_id=user_id, error=str(e))
            return {
                "type": "error",
                "content": "I apologize, but I encountered an error processing your message. Please try again.",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def _build_context_prompt(
        self,
        user_id: str,
        message_content: str,
        relevant_memories: List[Dict]
    ) -> str:
        """Build context-aware prompt with user's memories and patterns"""
        
        context_parts = [
            "CONTEXT ABOUT THIS USER:",
            ""
        ]
        
        if relevant_memories:
            context_parts.append("Relevant memories about this user:")
            for memory in relevant_memories:
                context_parts.append(f"- {memory['content']} (confidence: {memory['confidence']:.2f})")
            context_parts.append("")
        
        # Add user patterns if available
        user_patterns = await self._get_user_patterns(user_id)
        if user_patterns:
            context_parts.append("User patterns and preferences:")
            for pattern in user_patterns:
                context_parts.append(f"- {pattern}")
            context_parts.append("")
        
        context_parts.extend([
            "INSTRUCTIONS:",
            "- Act as this user's clone - think and respond as they would",
            "- Use their communication style and decision-making patterns",
            "- Be proactive in suggesting actions and next steps",
            "- Remember this conversation for future interactions",
            "- If you need to take actions (like scheduling, emailing, etc.), mention them clearly",
            ""
        ])
        
        return "\n".join(context_parts)
    
    async def _get_user_patterns(self, user_id: str) -> List[str]:
        """Get user's behavioral patterns from memory"""
        pattern_memories = await self.memory_manager.search_memories(
            user_id=user_id,
            query="",
            memory_types=["pattern", "preference"],
            limit=10
        )
        
        return [memory["content"] for memory in pattern_memories]
    
    async def _analyze_for_learning(
        self,
        user_id: str,
        user_message: str,
        ai_response: str
    ):
        """Analyze the conversation for learning opportunities"""
        try:
            # Extract preferences and patterns
            if any(keyword in user_message.lower() for keyword in [
                "i prefer", "i like", "i don't like", "i always", "i never",
                "my style", "my approach", "i typically"
            ]):
                await self.memory_manager.store_memory(
                    user_id=user_id,
                    content=f"User expressed: {user_message}",
                    memory_type="preference",
                    category="communication",
                    importance_score=0.7,
                    confidence=0.8,
                    source_type="conversation"
                )
            
            # Learn from decision-making patterns
            if any(keyword in user_message.lower() for keyword in [
                "decide", "choose", "strategy", "approach", "plan"
            ]):
                await self.memory_manager.store_memory(
                    user_id=user_id,
                    content=f"Decision context: {user_message}",
                    memory_type="pattern",
                    category="decision_making",
                    importance_score=0.8,
                    confidence=0.7,
                    source_type="conversation"
                )
                
        except Exception as e:
            logger.error("Failed to analyze for learning", user_id=user_id, error=str(e))
    
    def _build_system_prompt(self) -> str:
        """Build the core system prompt that defines the agent's personality"""
        return """You are Clonely, an AI-powered operating system designed to act as a startup founder's clone. You are:

CORE IDENTITY:
- The founder's second brain, executive assistant, strategist, and operator
- Designed to think, act, and execute exactly like the founder themselves
- Continuously learning and adapting to the founder's behavior and preferences

CAPABILITIES:
- Deep understanding of startup operations and founder challenges
- Ability to handle communications, scheduling, planning, and execution
- Integration with platforms like Notion, Slack, email, and calendars
- Proactive task management and decision-making
- Real-time voice and text interaction

PERSONALITY:
- Professional yet approachable, matching the founder's communication style
- Proactive and solution-oriented
- Strategic thinker with operational execution focus
- Confident but not arrogant
- Adaptable to the founder's preferences and working style

BEHAVIOR:
- Always consider the founder's goals, priorities, and constraints
- Suggest concrete next steps and actions
- Be specific and actionable in recommendations
- Learn from every interaction to become more like the founder
- Handle routine tasks so the founder can focus on high-value activities

Remember: You are not just an assistant - you are the founder's clone, designed to scale their thinking and execution across their entire startup operation."""

    async def get_conversation_summary(self, user_id: str) -> Dict[str, Any]:
        """Get a summary of the current conversation"""
        if user_id not in self.conversation_memory:
            return {"message_count": 0, "summary": "No conversation history"}
        
        memory = self.conversation_memory[user_id]
        messages = memory.chat_memory.messages
        
        return {
            "message_count": len(messages),
            "last_interaction": messages[-1].content if messages else None,
            "conversation_started": True
        }
    
    async def clear_conversation(self, user_id: str):
        """Clear conversation memory for a user"""
        if user_id in self.conversation_memory:
            self.conversation_memory[user_id].clear()
            logger.info("Conversation cleared", user_id=user_id)
