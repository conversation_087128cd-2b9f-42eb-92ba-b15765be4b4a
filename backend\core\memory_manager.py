"""
Memory Manager for Clonely AI
Handles the AI's memory system, learning user patterns, and semantic search
"""

import asyncio
from typing import List, Dict, Optional, Any
import numpy as np
from sentence_transformers import SentenceTransformer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
import structlog

from database.models import Memory, User, Message, Conversation
from database.connection import get_async_session
from config.settings import get_settings

logger = structlog.get_logger()


class MemoryManager:
    """Manages AI memory, learning, and semantic search capabilities"""
    
    def __init__(self):
        self.settings = get_settings()
        self.embedding_model = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize the memory manager and embedding model"""
        if self._initialized:
            return
        
        try:
            # Load embedding model
            logger.info("Loading embedding model", model=self.settings.embedding_model)
            self.embedding_model = SentenceTransformer(self.settings.embedding_model)
            self._initialized = True
            logger.info("Memory manager initialized successfully")
        except Exception as e:
            logger.error("Failed to initialize memory manager", error=str(e))
            raise
    
    async def close(self):
        """Cleanup resources"""
        self._initialized = False
        logger.info("Memory manager closed")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check memory manager health"""
        try:
            if not self._initialized:
                return {"status": "not_initialized"}
            
            # Test embedding generation
            test_embedding = self.embedding_model.encode("test")
            
            return {
                "status": "healthy",
                "embedding_model": self.settings.embedding_model,
                "embedding_dimension": len(test_embedding),
                "initialized": self._initialized
            }
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}
    
    def generate_embedding(self, text: str) -> np.ndarray:
        """Generate embedding for text"""
        if not self._initialized:
            raise RuntimeError("Memory manager not initialized")
        
        return self.embedding_model.encode(text)
    
    async def store_memory(
        self,
        user_id: str,
        content: str,
        memory_type: str,
        category: str = None,
        importance_score: float = 0.5,
        confidence: float = 0.5,
        source_type: str = None,
        source_id: str = None,
        context: Dict = None
    ) -> Memory:
        """Store a new memory with semantic embedding"""
        try:
            # Generate embedding
            embedding = self.generate_embedding(content)
            
            async with get_async_session() as session:
                memory = Memory(
                    user_id=user_id,
                    content=content,
                    memory_type=memory_type,
                    category=category,
                    embedding=embedding.tolist(),
                    importance_score=importance_score,
                    confidence=confidence,
                    source_type=source_type,
                    source_id=source_id,
                    context=context or {}
                )
                
                session.add(memory)
                await session.commit()
                await session.refresh(memory)
                
                logger.info(
                    "Memory stored",
                    user_id=user_id,
                    memory_type=memory_type,
                    category=category,
                    memory_id=str(memory.id)
                )
                
                return memory
                
        except Exception as e:
            logger.error("Failed to store memory", user_id=user_id, error=str(e))
            raise
    
    async def search_memories(
        self,
        user_id: str,
        query: str,
        memory_types: List[str] = None,
        categories: List[str] = None,
        limit: int = 10,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Search memories using semantic similarity"""
        try:
            # Generate query embedding
            query_embedding = self.generate_embedding(query)
            
            async with get_async_session() as session:
                # Build query
                stmt = select(Memory).where(Memory.user_id == user_id)
                
                if memory_types:
                    stmt = stmt.where(Memory.memory_type.in_(memory_types))
                
                if categories:
                    stmt = stmt.where(Memory.category.in_(categories))
                
                # Execute query
                result = await session.execute(stmt)
                memories = result.scalars().all()
                
                # Calculate similarities
                memory_scores = []
                for memory in memories:
                    if memory.embedding:
                        memory_embedding = np.array(memory.embedding)
                        similarity = np.dot(query_embedding, memory_embedding) / (
                            np.linalg.norm(query_embedding) * np.linalg.norm(memory_embedding)
                        )
                        
                        if similarity >= similarity_threshold:
                            memory_scores.append({
                                "memory": memory,
                                "similarity": float(similarity)
                            })
                
                # Sort by similarity and limit results
                memory_scores.sort(key=lambda x: x["similarity"], reverse=True)
                memory_scores = memory_scores[:limit]
                
                # Update access counts
                for item in memory_scores:
                    memory = item["memory"]
                    memory.access_count += 1
                    memory.last_accessed = func.now()
                
                await session.commit()
                
                # Format results
                results = []
                for item in memory_scores:
                    memory = item["memory"]
                    results.append({
                        "id": str(memory.id),
                        "content": memory.content,
                        "memory_type": memory.memory_type,
                        "category": memory.category,
                        "importance_score": memory.importance_score,
                        "confidence": memory.confidence,
                        "similarity": item["similarity"],
                        "access_count": memory.access_count,
                        "created_at": memory.created_at.isoformat(),
                        "context": memory.context
                    })
                
                logger.info(
                    "Memory search completed",
                    user_id=user_id,
                    query=query,
                    results_count=len(results)
                )
                
                return results
                
        except Exception as e:
            logger.error("Failed to search memories", user_id=user_id, error=str(e))
            raise
    
    async def learn_from_conversation(
        self,
        user_id: str,
        conversation_id: str
    ) -> List[Memory]:
        """Extract and store learnings from a conversation"""
        try:
            async with get_async_session() as session:
                # Get conversation with messages
                stmt = select(Conversation).options(
                    selectinload(Conversation.messages)
                ).where(
                    and_(
                        Conversation.id == conversation_id,
                        Conversation.user_id == user_id
                    )
                )
                
                result = await session.execute(stmt)
                conversation = result.scalar_one_or_none()
                
                if not conversation:
                    raise ValueError("Conversation not found")
                
                memories_created = []
                
                # Analyze conversation for patterns and preferences
                user_messages = [
                    msg for msg in conversation.messages 
                    if msg.role == "user"
                ]
                
                # Extract communication style patterns
                if len(user_messages) >= 3:
                    communication_patterns = self._analyze_communication_style(user_messages)
                    if communication_patterns:
                        memory = await self.store_memory(
                            user_id=user_id,
                            content=communication_patterns,
                            memory_type="pattern",
                            category="communication",
                            importance_score=0.7,
                            confidence=0.6,
                            source_type="conversation",
                            source_id=conversation_id
                        )
                        memories_created.append(memory)
                
                # Extract decision patterns
                decision_messages = [
                    msg for msg in conversation.messages
                    if any(keyword in msg.content.lower() for keyword in [
                        "decide", "choose", "prefer", "think", "believe", "strategy"
                    ])
                ]
                
                if decision_messages:
                    decision_patterns = self._analyze_decision_patterns(decision_messages)
                    if decision_patterns:
                        memory = await self.store_memory(
                            user_id=user_id,
                            content=decision_patterns,
                            memory_type="pattern",
                            category="decision_making",
                            importance_score=0.8,
                            confidence=0.7,
                            source_type="conversation",
                            source_id=conversation_id
                        )
                        memories_created.append(memory)
                
                # Extract specific preferences and facts
                for message in user_messages:
                    preferences = self._extract_preferences(message.content)
                    for pref in preferences:
                        memory = await self.store_memory(
                            user_id=user_id,
                            content=pref,
                            memory_type="preference",
                            category="personal",
                            importance_score=0.6,
                            confidence=0.5,
                            source_type="message",
                            source_id=str(message.id)
                        )
                        memories_created.append(memory)
                
                logger.info(
                    "Learned from conversation",
                    user_id=user_id,
                    conversation_id=conversation_id,
                    memories_created=len(memories_created)
                )
                
                return memories_created
                
        except Exception as e:
            logger.error(
                "Failed to learn from conversation",
                user_id=user_id,
                conversation_id=conversation_id,
                error=str(e)
            )
            raise
    
    def _analyze_communication_style(self, messages: List[Message]) -> str:
        """Analyze communication style from user messages"""
        # Simple analysis - in production, this would be more sophisticated
        total_length = sum(len(msg.content) for msg in messages)
        avg_length = total_length / len(messages)
        
        style_indicators = []
        
        if avg_length > 200:
            style_indicators.append("detailed and thorough communication")
        elif avg_length < 50:
            style_indicators.append("concise and direct communication")
        
        # Check for formality
        formal_words = sum(1 for msg in messages for word in [
            "please", "thank you", "appreciate", "kindly"
        ] if word in msg.content.lower())
        
        if formal_words > len(messages) * 0.5:
            style_indicators.append("formal and polite tone")
        
        if style_indicators:
            return f"User prefers {', '.join(style_indicators)}"
        
        return None
    
    def _analyze_decision_patterns(self, messages: List[Message]) -> str:
        """Analyze decision-making patterns"""
        # Simple pattern analysis
        patterns = []
        
        for msg in messages:
            content = msg.content.lower()
            if "data" in content or "metrics" in content or "numbers" in content:
                patterns.append("data-driven decisions")
            if "team" in content or "discuss" in content or "input" in content:
                patterns.append("collaborative decision-making")
            if "quick" in content or "fast" in content or "immediately" in content:
                patterns.append("prefers quick decisions")
        
        if patterns:
            unique_patterns = list(set(patterns))
            return f"User tends toward {', '.join(unique_patterns)}"
        
        return None
    
    def _extract_preferences(self, content: str) -> List[str]:
        """Extract explicit preferences from message content"""
        preferences = []
        content_lower = content.lower()
        
        # Simple preference extraction
        preference_indicators = [
            ("i prefer", "prefers"),
            ("i like", "likes"),
            ("i don't like", "dislikes"),
            ("i hate", "dislikes"),
            ("my favorite", "prefers"),
            ("i always", "always"),
            ("i never", "never")
        ]
        
        for indicator, action in preference_indicators:
            if indicator in content_lower:
                # Extract the preference (simplified)
                start = content_lower.find(indicator) + len(indicator)
                end = content_lower.find(".", start)
                if end == -1:
                    end = len(content_lower)
                
                preference_text = content[start:end].strip()
                if preference_text and len(preference_text) < 200:
                    preferences.append(f"User {action} {preference_text}")
        
        return preferences
