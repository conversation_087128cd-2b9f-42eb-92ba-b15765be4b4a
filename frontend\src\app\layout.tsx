import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from './providers';
import { Toaster } from '@/components/ui/toaster';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Clonely - AI Operating System for Startup Founders',
  description: 'An AI-powered operating system that thinks, acts, and executes like the founder themselves. Your second brain, executive assistant, strategist, and operator - all in one.',
  keywords: ['AI', 'startup', 'founder', 'operating system', 'productivity', 'automation'],
  authors: [{ name: '<PERSON><PERSON>ly Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#0ea5e9',
  openGraph: {
    title: 'Clonely - AI Operating System for Startup Founders',
    description: 'An AI-powered operating system that thinks, acts, and executes like the founder themselves.',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Clonely - AI Operating System for Startup Founders',
    description: 'An AI-powered operating system that thinks, acts, and executes like the founder themselves.',
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} h-full bg-gray-50 antialiased`}>
        <Providers>
          <div className="min-h-full">
            {children}
          </div>
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
