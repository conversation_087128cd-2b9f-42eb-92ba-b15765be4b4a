"""
Configuration settings for <PERSON><PERSON>ly backend
Manages environment variables and application configuration
"""

from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional
import os


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application settings
    app_name: str = "Clonely"
    debug: bool = Field(default=False, env="DEBUG")
    environment: str = Field(default="development", env="ENVIRONMENT")
    
    # Database settings
    database_url: str = Field(
        default="postgresql+asyncpg://clonely:clonely@localhost:5432/clonely",
        env="DATABASE_URL"
    )
    
    # Redis settings for caching and sessions
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # AI/LLM API keys
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    
    # Default AI model settings
    default_llm_provider: str = Field(default="openai", env="DEFAULT_LLM_PROVIDER")
    default_model: str = Field(default="gpt-4-turbo-preview", env="DEFAULT_MODEL")
    
    # Security settings
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        env="SECRET_KEY"
    )
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # CORS settings
    allowed_origins: list[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000"
    ]
    
    # Memory and learning settings
    memory_retention_days: int = Field(default=365, env="MEMORY_RETENTION_DAYS")
    embedding_model: str = Field(
        default="sentence-transformers/all-MiniLM-L6-v2",
        env="EMBEDDING_MODEL"
    )
    vector_dimension: int = 384  # Dimension for the default embedding model
    
    # Integration settings
    notion_client_id: Optional[str] = Field(default=None, env="NOTION_CLIENT_ID")
    notion_client_secret: Optional[str] = Field(default=None, env="NOTION_CLIENT_SECRET")
    slack_client_id: Optional[str] = Field(default=None, env="SLACK_CLIENT_ID")
    slack_client_secret: Optional[str] = Field(default=None, env="SLACK_CLIENT_SECRET")
    
    # Voice interface settings
    enable_voice_interface: bool = Field(default=True, env="ENABLE_VOICE_INTERFACE")
    speech_to_text_provider: str = Field(default="openai", env="STT_PROVIDER")
    text_to_speech_provider: str = Field(default="openai", env="TTS_PROVIDER")
    
    # Performance settings
    max_concurrent_requests: int = Field(default=100, env="MAX_CONCURRENT_REQUESTS")
    request_timeout_seconds: int = Field(default=30, env="REQUEST_TIMEOUT_SECONDS")
    
    # Logging settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    enable_structured_logging: bool = Field(default=True, env="ENABLE_STRUCTURED_LOGGING")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get application settings (singleton pattern)"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


def reload_settings() -> Settings:
    """Reload settings (useful for testing)"""
    global _settings
    _settings = Settings()
    return _settings
