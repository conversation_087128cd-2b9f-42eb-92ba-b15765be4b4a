# Clonely Environment Configuration
# Copy this file to .env and update the values

# Application Settings
DEBUG=true
ENVIRONMENT=development
SECRET_KEY=your-secret-key-change-in-production

# Database Configuration
DATABASE_URL=postgresql+asyncpg://clonely:clonely@localhost:5432/clonely

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# AI/LLM API Keys
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Default AI Model Settings
DEFAULT_LLM_PROVIDER=openai
DEFAULT_MODEL=gpt-4-turbo-preview

# Memory and Learning Settings
MEMORY_RETENTION_DAYS=365
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# Integration API Keys
NOTION_CLIENT_ID=your-notion-client-id
NOTION_CLIENT_SECRET=your-notion-client-secret
SLACK_CLIENT_ID=your-slack-client-id
SLACK_CLIENT_SECRET=your-slack-client-secret

# Voice Interface Settings
ENABLE_VOICE_INTERFACE=true
STT_PROVIDER=openai
TTS_PROVIDER=openai

# Performance Settings
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT_SECONDS=30

# Logging Settings
LOG_LEVEL=INFO
ENABLE_STRUCTURED_LOGGING=true

# Frontend Settings
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
