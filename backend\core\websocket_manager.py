"""
WebSocket Connection Manager for <PERSON><PERSON>ly
Handles real-time communication between the AI agent and users
"""

from fastapi import WebSocket
from typing import Dict, List
import json
import asyncio
import structlog

logger = structlog.get_logger()


class ConnectionManager:
    """Manages WebSocket connections for real-time communication"""
    
    def __init__(self):
        # Store active connections by user_id
        self.active_connections: Dict[str, WebSocket] = {}
        # Store user sessions and context
        self.user_sessions: Dict[str, dict] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        
        # Initialize user session if not exists
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = {
                "connected_at": asyncio.get_event_loop().time(),
                "message_count": 0,
                "context": {}
            }
        
        logger.info("WebSocket connection established", user_id=user_id)
        
        # Send welcome message
        await self.send_personal_message(
            message=json.dumps({
                "type": "system",
                "message": "Connected to Clonely AI. I'm ready to assist you!",
                "timestamp": asyncio.get_event_loop().time()
            }),
            user_id=user_id
        )
    
    def disconnect(self, user_id: str):
        """Remove a WebSocket connection"""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        
        # Keep session data for potential reconnection
        logger.info("WebSocket connection closed", user_id=user_id)
    
    async def send_personal_message(self, message: str, user_id: str):
        """Send a message to a specific user"""
        if user_id in self.active_connections:
            try:
                websocket = self.active_connections[user_id]
                await websocket.send_text(message)
                
                # Update session stats
                if user_id in self.user_sessions:
                    self.user_sessions[user_id]["message_count"] += 1
                    
            except Exception as e:
                logger.error("Failed to send message", user_id=user_id, error=str(e))
                # Remove broken connection
                self.disconnect(user_id)
    
    async def broadcast_message(self, message: str):
        """Send a message to all connected users"""
        disconnected_users = []
        
        for user_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(message)
            except Exception as e:
                logger.error("Failed to broadcast to user", user_id=user_id, error=str(e))
                disconnected_users.append(user_id)
        
        # Clean up disconnected users
        for user_id in disconnected_users:
            self.disconnect(user_id)
    
    async def send_typing_indicator(self, user_id: str, is_typing: bool = True):
        """Send typing indicator to user"""
        message = json.dumps({
            "type": "typing",
            "is_typing": is_typing,
            "timestamp": asyncio.get_event_loop().time()
        })
        await self.send_personal_message(message, user_id)
    
    async def send_agent_status(self, user_id: str, status: str, details: dict = None):
        """Send agent status update to user"""
        message = json.dumps({
            "type": "agent_status",
            "status": status,
            "details": details or {},
            "timestamp": asyncio.get_event_loop().time()
        })
        await self.send_personal_message(message, user_id)
    
    async def send_proactive_notification(self, user_id: str, notification: dict):
        """Send proactive notification from the AI agent"""
        message = json.dumps({
            "type": "proactive_notification",
            "notification": notification,
            "timestamp": asyncio.get_event_loop().time()
        })
        await self.send_personal_message(message, user_id)
    
    def get_connected_users(self) -> List[str]:
        """Get list of currently connected user IDs"""
        return list(self.active_connections.keys())
    
    def get_user_session(self, user_id: str) -> dict:
        """Get user session data"""
        return self.user_sessions.get(user_id, {})
    
    def update_user_context(self, user_id: str, context_update: dict):
        """Update user session context"""
        if user_id in self.user_sessions:
            self.user_sessions[user_id]["context"].update(context_update)
    
    async def handle_heartbeat(self, user_id: str):
        """Handle heartbeat/ping from client"""
        message = json.dumps({
            "type": "pong",
            "timestamp": asyncio.get_event_loop().time()
        })
        await self.send_personal_message(message, user_id)
