# Clonely Development Makefile

.PHONY: help setup dev build test clean logs shell db-migrate db-reset

# Default target
help:
	@echo "Clonely Development Commands:"
	@echo ""
	@echo "Setup:"
	@echo "  setup          - Initial project setup"
	@echo "  env            - Copy environment file"
	@echo ""
	@echo "Development:"
	@echo "  dev            - Start development environment"
	@echo "  dev-backend    - Start only backend services"
	@echo "  dev-frontend   - Start only frontend"
	@echo "  build          - Build all services"
	@echo ""
	@echo "Database:"
	@echo "  db-migrate     - Run database migrations"
	@echo "  db-reset       - Reset database"
	@echo "  db-shell       - Connect to database shell"
	@echo ""
	@echo "Testing:"
	@echo "  test           - Run all tests"
	@echo "  test-backend   - Run backend tests"
	@echo "  test-frontend  - Run frontend tests"
	@echo ""
	@echo "Utilities:"
	@echo "  logs           - Show all service logs"
	@echo "  shell          - Open backend shell"
	@echo "  clean          - Clean up containers and volumes"
	@echo "  install        - Install dependencies"

# Setup commands
setup: env install
	@echo "✅ Clonely setup complete!"
	@echo "Run 'make dev' to start the development environment"

env:
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "📝 Created .env file from .env.example"; \
		echo "⚠️  Please update the API keys in .env file"; \
	else \
		echo "✅ .env file already exists"; \
	fi

install:
	@echo "📦 Installing dependencies..."
	cd frontend && npm install
	@echo "✅ Dependencies installed"

# Development commands
dev:
	@echo "🚀 Starting Clonely development environment..."
	docker-compose up --build

dev-backend:
	@echo "🚀 Starting backend services..."
	docker-compose up postgres redis backend

dev-frontend:
	@echo "🚀 Starting frontend..."
	cd frontend && npm run dev

build:
	@echo "🔨 Building all services..."
	docker-compose build

# Database commands
db-migrate:
	@echo "🗄️  Running database migrations..."
	docker-compose exec backend alembic upgrade head

db-reset:
	@echo "🗄️  Resetting database..."
	docker-compose down -v
	docker-compose up -d postgres redis
	sleep 5
	docker-compose exec backend alembic upgrade head

db-shell:
	@echo "🗄️  Connecting to database..."
	docker-compose exec postgres psql -U clonely -d clonely

# Testing commands
test:
	@echo "🧪 Running all tests..."
	make test-backend
	make test-frontend

test-backend:
	@echo "🧪 Running backend tests..."
	docker-compose exec backend pytest

test-frontend:
	@echo "🧪 Running frontend tests..."
	cd frontend && npm test

# Utility commands
logs:
	@echo "📋 Showing service logs..."
	docker-compose logs -f

logs-backend:
	@echo "📋 Showing backend logs..."
	docker-compose logs -f backend

logs-frontend:
	@echo "📋 Showing frontend logs..."
	docker-compose logs -f frontend

shell:
	@echo "🐚 Opening backend shell..."
	docker-compose exec backend /bin/bash

clean:
	@echo "🧹 Cleaning up..."
	docker-compose down -v --remove-orphans
	docker system prune -f

# Production commands
prod-build:
	@echo "🏭 Building for production..."
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

prod-up:
	@echo "🏭 Starting production environment..."
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Health checks
health:
	@echo "🏥 Checking service health..."
	@curl -f http://localhost:8000/api/health || echo "❌ Backend unhealthy"
	@curl -f http://localhost:3000 || echo "❌ Frontend unhealthy"

# Development helpers
format:
	@echo "🎨 Formatting code..."
	cd backend && black . && isort .
	cd frontend && npm run lint:fix

lint:
	@echo "🔍 Linting code..."
	cd backend && black --check . && isort --check-only .
	cd frontend && npm run lint

# Quick start for new developers
quick-start: setup dev
	@echo ""
	@echo "🎉 Clonely is starting up!"
	@echo ""
	@echo "📱 Frontend: http://localhost:3000"
	@echo "🔧 Backend API: http://localhost:8000"
	@echo "📚 API Docs: http://localhost:8000/api/docs"
	@echo ""
	@echo "⚠️  Don't forget to:"
	@echo "   1. Update API keys in .env file"
	@echo "   2. Run 'make db-migrate' after services are up"
